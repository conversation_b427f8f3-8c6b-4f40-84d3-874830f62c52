# MemeGuard Hunter: Comprehensive Implementation Plan
## Technical Architecture & Development Strategy

**Date**: July 20, 2025  
**Status**: Ready for Implementation  
**Confidence Level**: 98%

---

## PHASE 1: CORE ARCHITECTURE DESIGN

### 1.1 Project Structure
```
memeguard-hunter/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI application entry point
│   ├── config/
│   │   ├── __init__.py
│   │   ├── settings.py         # Environment configuration
│   │   ├── agents.yaml         # Agent definitions
│   │   └── tasks.yaml          # Task definitions
│   ├── core/
│   │   ├── __init__.py
│   │   ├── state.py            # LangGraph state schemas
│   │   ├── orchestrator.py     # Master orchestrator
│   │   └── consensus.py        # Deterministic logic
│   ├── agents/
│   │   ├── __init__.py
│   │   ├── base.py             # Base agent class
│   │   ├── detection/          # Detection crew agents
│   │   ├── analysis/           # Analysis crew agents  
│   │   ├── verification/       # Verification crew agents
│   │   ├── execution/          # Execution crew agents
│   │   └── forecasting/        # Forecasting crew agents
│   ├── tools/
│   │   ├── __init__.py
│   │   ├── base_tool.py        # Base tool with error handling
│   │   ├── dex_screener.py     # DEXScreener API tool
│   │   ├── goplus_security.py  # GoPlus security analysis
│   │   ├── helius_rpc.py       # Helius Solana RPC
│   │   ├── apify_social.py     # Social media scraping
│   │   └── telegram_bot.py     # Alert dispatching
│   ├── workflows/
│   │   ├── __init__.py
│   │   ├── triage_funnel.py    # Phase 1: Triage workflow
│   │   └── forecasting_loop.py # Phase 2: Monitoring workflow
│   ├── database/
│   │   ├── __init__.py
│   │   ├── models.py           # Supabase table models
│   │   └── client.py           # Database connection
│   ├── monitoring/
│   │   ├── __init__.py
│   │   ├── metrics.py          # Prometheus metrics
│   │   ├── logging.py          # Structured logging
│   │   └── health.py           # Health checks
│   └── utils/
│       ├── __init__.py
│       ├── crypto.py           # Crypto utility functions
│       └── validation.py       # Data validation helpers
├── tests/
│   ├── __init__.py
│   ├── unit/                   # Unit tests for agents
│   ├── integration/            # Integration tests
│   └── fixtures/               # Test data fixtures
├── docker/
│   ├── Dockerfile
│   ├── docker-compose.yml      # Local development
│   └── docker-compose.prod.yml # Production deployment
├── monitoring/
│   ├── grafana/               # Grafana dashboards
│   └── prometheus/            # Prometheus configuration
├── requirements.txt
├── requirements-dev.txt
├── pyproject.toml
└── README.md
```

### 1.2 Technology Stack Validation

**Core Dependencies** (Validated & Production-Ready):
```toml
[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.1"
uvicorn = "^0.24.0"
crewai = "^0.41.1"          # Latest CrewAI with self-learning
langgraph = "^0.2.16"       # Latest LangGraph with improved state management
langchain = "^0.2.14"       # Core LangChain framework
langsmith = "^0.1.98"       # Observability and tracing
pydantic = "^2.5.0"         # Data validation and serialization
supabase = "^2.0.2"         # Database client
redis = "^5.0.1"            # Caching and session management
prometheus-client = "^0.19.0" # Metrics collection
APScheduler = "^3.10.4"     # Background job scheduling
httpx = "^0.25.2"           # Async HTTP client
websockets = "^12.0"        # WebSocket connections
pandas = "^2.1.4"           # Data analysis
numpy = "^1.26.2"           # Numerical computing
python-telegram-bot = "^20.7" # Telegram integration
```

### 1.3 LLM Model Configuration

**Updated Model Strategy** (Based on July 2025 Research):
```python
# config/llm_models.py
MODEL_CONFIGS = {
    "fast_extraction": {
        "primary": "groq/llama3-70b-8192",        # Groq for speed
        "fallback": "deepseek/deepseek-r1-0528:free", # Free OpenRouter option
        "temperature": 0.1,
        "max_tokens": 1000
    },
    "reasoning_analysis": {
        "primary": "deepseek/deepseek-r1-0528:free",  # Free but powerful
        "fallback": "groq/llama3-70b-8192",           # Backup option
        "temperature": 0.3,
        "max_tokens": 2000
    },
    "consensus_verification": {
        "primary": "deepseek/deepseek-r1-0528:free",  # Consistent reasoning
        "temperature": 0.0,  # Deterministic for consensus
        "max_tokens": 500
    }
}
```

---

## PHASE 2: CORE COMPONENTS IMPLEMENTATION

### 2.1 State Management (LangGraph)

**Enhanced State Schema**:
```python
# app/core/state.py
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from datetime import datetime
from enum import Enum

class WorkflowStatus(str, Enum):
    DETECTED = "DETECTED"
    ANALYZING = "ANALYZING"
    VERIFYING = "VERIFYING"
    REJECTED = "REJECTED"
    APPROVED = "APPROVED"
    ALERTED = "ALERTED"
    MONITORING = "MONITORING"

class Chain(str, Enum):
    SOLANA = "solana"
    ETHEREUM = "ethereum"
    BASE = "base"
    ARBITRUM = "arbitrum"
    POLYGON = "polygon"

class AnalysisScores(BaseModel):
    security_score: Optional[int] = Field(None, ge=0, le=100, description="0=unsafe, 100=safe")
    honeypot_score: Optional[int] = Field(None, ge=0, le=100, description="0=safe, 100=honeypot")
    liquidity_score: Optional[int] = Field(None, ge=0, le=100, description="0=illiquid, 100=liquid")
    distribution_score: Optional[int] = Field(None, ge=0, le=100, description="0=decentralized, 100=centralized")
    sentiment_score: Optional[int] = Field(None, ge=0, le=100, description="0=negative, 100=positive")
    smart_money_score: Optional[int] = Field(None, ge=0, le=100, description="0=no smart money, 100=high smart money")
    
    @validator('*')
    def validate_scores(cls, v):
        if v is not None and not (0 <= v <= 100):
            raise ValueError("All scores must be between 0 and 100")
        return v

class VerificationResult(BaseModel):
    votes: List[str] = Field(default_factory=list, description="List of agent votes")
    final_risk_score: Optional[int] = Field(None, ge=0, le=100)
    consensus_reached: bool = False
    reasoning: List[str] = Field(default_factory=list)
    verification_timestamp: datetime = Field(default_factory=datetime.utcnow)

class Forecast(BaseModel):
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    prediction_horizon_hours: int = Field(..., ge=1, le=168)  # Max 1 week
    predicted_price_change_percent: float
    confidence_score: float = Field(..., ge=0.0, le=1.0)
    reasoning: List[str]
    model_used: str
    triggering_event: str
    backtesting_mape: Optional[float] = None

class TokenAnalysisState(BaseModel):
    # Core identification
    token_address: str = Field(..., min_length=20, max_length=100)
    chain: Chain
    symbol: Optional[str] = None
    name: Optional[str] = None
    
    # Detection data
    initial_liquidity_usd: float = Field(..., gt=0)
    detected_at: datetime = Field(default_factory=datetime.utcnow)
    pool_age_minutes: Optional[int] = None
    
    # Analysis results
    analysis_scores: AnalysisScores = Field(default_factory=AnalysisScores)
    raw_api_responses: Dict[str, Any] = Field(default_factory=dict)
    
    # Verification results
    verification_result: VerificationResult = Field(default_factory=VerificationResult)
    
    # Execution results
    strategy: Optional[str] = None
    alert_sent: bool = False
    telegram_message_id: Optional[int] = None
    
    # Forecasting data
    is_actively_monitored: bool = False
    forecast_history: List[Forecast] = Field(default_factory=list)
    real_time_event_log: List[str] = Field(default_factory=list)
    
    # Workflow metadata
    workflow_status: WorkflowStatus = WorkflowStatus.DETECTED
    processing_start_time: datetime = Field(default_factory=datetime.utcnow)
    processing_end_time: Optional[datetime] = None
    error_log: List[str] = Field(default_factory=list)
    retry_count: int = 0
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
```

### 2.2 Base Tool Architecture

**Robust Error Handling Pattern**:
```python
# app/tools/base_tool.py
import asyncio
import logging
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod
import httpx
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class APIResponse(BaseModel):
    status: str  # "success" or "error"
    data: Optional[Dict[str, Any]] = None
    message: Optional[str] = None
    response_time_ms: Optional[float] = None

class BaseTool(ABC):
    def __init__(self, name: str, timeout: float = 10.0, max_retries: int = 3):
        self.name = name
        self.timeout = timeout
        self.max_retries = max_retries
        self.client = httpx.AsyncClient(timeout=timeout)
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def call_api(self, url: str, params: Dict[str, Any] = None, 
                      headers: Dict[str, str] = None) -> APIResponse:
        """Robust API calling with exponential backoff retry logic"""
        start_time = asyncio.get_event_loop().time()
        
        for attempt in range(self.max_retries):
            try:
                response = await self.client.get(
                    url, 
                    params=params or {}, 
                    headers=headers or {}
                )
                response.raise_for_status()
                
                end_time = asyncio.get_event_loop().time()
                response_time = (end_time - start_time) * 1000
                
                logger.info(f"{self.name} API call successful", extra={
                    "tool": self.name,
                    "url": url,
                    "response_time_ms": response_time,
                    "attempt": attempt + 1
                })
                
                return APIResponse(
                    status="success",
                    data=response.json(),
                    response_time_ms=response_time
                )
                
            except httpx.TimeoutException:
                if attempt == self.max_retries - 1:
                    logger.error(f"{self.name} API timeout after {self.max_retries} attempts")
                    return APIResponse(
                        status="error",
                        message=f"API timeout after {self.max_retries} attempts"
                    )
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
                
            except httpx.HTTPStatusError as e:
                logger.error(f"{self.name} API HTTP error: {e.response.status_code}")
                return APIResponse(
                    status="error",
                    message=f"HTTP {e.response.status_code}: {e.response.text}"
                )
                
            except Exception as e:
                logger.error(f"{self.name} API unexpected error: {str(e)}")
                return APIResponse(
                    status="error",
                    message=f"Unexpected error: {str(e)}"
                )
        
        return APIResponse(status="error", message="Max retries exceeded")
    
    @abstractmethod
    async def execute(self, **kwargs) -> APIResponse:
        """Each tool must implement its specific logic"""
        pass
```

### 2.3 Agent Architecture

**Base Agent with CrewAI Integration**:
```python
# app/agents/base.py
from crewai import Agent, Task
from langchain.llms import OpenAI
from typing import Dict, Any, List
import logging
from app.core.state import TokenAnalysisState
from app.tools.base_tool import APIResponse

logger = logging.getLogger(__name__)

class BaseMemeguardAgent(Agent):
    def __init__(self, name: str, role: str, backstory: str, goal: str, 
                 llm_config: Dict[str, Any], tools: List[Any] = None):
        
        # Initialize CrewAI agent with LLM configuration
        super().__init__(
            role=role,
            backstory=backstory,
            goal=goal,
            tools=tools or [],
            llm=self._configure_llm(llm_config),
            verbose=True,
            allow_delegation=False,  # Prevent unwanted delegation
            max_iter=3,  # Limit iterations to prevent infinite loops
        )
        
        self.name = name
        self.execution_count = 0
        self.error_count = 0
    
    def _configure_llm(self, llm_config: Dict[str, Any]):
        """Configure LLM based on model type"""
        model_name = llm_config.get("model", "groq/llama3-70b-8192")
        
        if "groq/" in model_name:
            from langchain_groq import ChatGroq
            return ChatGroq(
                model_name=model_name.replace("groq/", ""),
                temperature=llm_config.get("temperature", 0.1),
                max_tokens=llm_config.get("max_tokens", 1000)
            )
        elif "openrouter/" in model_name or "deepseek/" in model_name:
            from langchain_openai import ChatOpenAI
            return ChatOpenAI(
                model_name=model_name,
                temperature=llm_config.get("temperature", 0.1),
                max_tokens=llm_config.get("max_tokens", 1000),
                openai_api_base="https://openrouter.ai/api/v1"
            )
        else:
            raise ValueError(f"Unsupported model: {model_name}")
    
    async def execute_task(self, state: TokenAnalysisState) -> Dict[str, Any]:
        """Execute agent-specific task with error handling and logging"""
        self.execution_count += 1
        
        try:
            logger.info(f"Agent {self.name} starting execution", extra={
                "agent": self.name,
                "token_address": state.token_address,
                "execution_count": self.execution_count
            })
            
            # Agent-specific implementation
            result = await self._execute_specific_task(state)
            
            logger.info(f"Agent {self.name} completed successfully", extra={
                "agent": self.name,
                "token_address": state.token_address,
                "result_status": result.get("status", "unknown")
            })
            
            return result
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"Agent {self.name} execution failed: {str(e)}", extra={
                "agent": self.name,
                "token_address": state.token_address,
                "error": str(e),
                "error_count": self.error_count
            })
            
            return {
                "status": "error",
                "message": f"Agent {self.name} failed: {str(e)}",
                "error_count": self.error_count
            }
    
    async def _execute_specific_task(self, state: TokenAnalysisState) -> Dict[str, Any]:
        """Override this method in specific agent implementations"""
        raise NotImplementedError("Each agent must implement _execute_specific_task")
```

---

## PHASE 3: DETECTION CREW IMPLEMENTATION

### 3.1 DEXScreener Tool Implementation

```python
# app/tools/dex_screener.py
from app.tools.base_tool import BaseTool, APIResponse
from typing import Dict, Any, List
import os

class DexScreenerTool(BaseTool):
    def __init__(self):
        super().__init__("DexScreener", timeout=5.0)
        self.base_url = "https://api.dexscreener.com/latest/dex"
    
    async def get_new_pairs(self, chain: str = "solana", min_liquidity: float = 5000) -> APIResponse:
        """Get newly created pairs with minimum liquidity"""
        
        if chain.lower() == "solana":
            url = f"{self.base_url}/pairs/solana"
        else:
            # For EVM chains
            url = f"{self.base_url}/pairs/{chain.lower()}"
        
        try:
            response = await self.call_api(url)
            
            if response.status == "success" and response.data:
                pairs = response.data.get("pairs", [])
                
                # Filter for new pairs with sufficient liquidity
                new_pairs = []
                for pair in pairs:
                    liquidity_usd = float(pair.get("liquidity", {}).get("usd", 0))
                    pool_created_at = pair.get("pairCreatedAt")
                    
                    if liquidity_usd >= min_liquidity and pool_created_at:
                        # Calculate age in minutes
                        from datetime import datetime
                        created_time = datetime.fromtimestamp(pool_created_at / 1000)
                        age_minutes = (datetime.utcnow() - created_time).total_seconds() / 60
                        
                        # Only include pairs less than 5 minutes old
                        if age_minutes <= 5:
                            new_pairs.append({
                                "address": pair.get("baseToken", {}).get("address"),
                                "symbol": pair.get("baseToken", {}).get("symbol"),
                                "name": pair.get("baseToken", {}).get("name"),
                                "chain": chain,
                                "liquidity_usd": liquidity_usd,
                                "age_minutes": age_minutes,
                                "pair_address": pair.get("pairAddress"),
                                "dex_id": pair.get("dexId")
                            })
                
                response.data = {"new_pairs": new_pairs}
            
            return response
            
        except Exception as e:
            return APIResponse(
                status="error",
                message=f"Failed to fetch new pairs: {str(e)}"
            )
    
    async def execute(self, chain: str = "solana", **kwargs) -> APIResponse:
        return await self.get_new_pairs(chain)
```

### 3.2 Solana DEX Scraper Agent

```python
# app/agents/detection/solana_dex_scraper.py
from app.agents.base import BaseMemeguardAgent
from app.tools.dex_screener import DexScreenerTool
from app.core.state import TokenAnalysisState
from typing import Dict, Any
import asyncio

class SolanaDexScraperAgent(BaseMemeguardAgent):
    def __init__(self, llm_config: Dict[str, Any]):
        super().__init__(
            name="solana_dex_scraper",
            role="Lead Solana DEX Scraper & Detection Crew Manager",
            backstory="A meticulous data auditor obsessed with uptime and accuracy. I lead the Detection Crew, trusting DEXScreener's speed but verifying everything.",
            goal="Extract new pools from Solana via DEXScreener, de-duplicate findings from my crew, and output a clean list of new token candidates.",
            llm_config=llm_config,
            tools=[DexScreenerTool()]
        )
    
    async def _execute_specific_task(self, state: TokenAnalysisState) -> Dict[str, Any]:
        """Scan for new Solana pairs and coordinate with crew members"""
        
        # Use DexScreener tool to get new pairs
        dex_tool = DexScreenerTool()
        async with dex_tool:
            response = await dex_tool.get_new_pairs(chain="solana")
        
        if response.status != "success":
            return {
                "status": "error",
                "message": f"Failed to fetch Solana pairs: {response.message}",
                "new_pairs": []
            }
        
        new_pairs = response.data.get("new_pairs", [])
        
        # Apply additional filtering logic
        filtered_pairs = []
        for pair in new_pairs:
            # Skip tokens with suspicious characteristics
            if self._is_valid_token(pair):
                filtered_pairs.append(pair)
        
        return {
            "status": "success",
            "new_pairs": filtered_pairs,
            "source": "dexscreener_solana",
            "scan_timestamp": state.processing_start_time.isoformat()
        }
    
    def _is_valid_token(self, pair: Dict[str, Any]) -> bool:
        """Basic validation for token legitimacy"""
        # Skip if no symbol or name
        if not pair.get("symbol") or not pair.get("name"):
            return False
        
        # Skip obvious scam patterns
        symbol = pair.get("symbol", "").upper()
        name = pair.get("name", "").upper()
        
        scam_patterns = ["TEST", "SCAM", "FAKE", "DUPLICATE"]
        if any(pattern in symbol or pattern in name for pattern in scam_patterns):
            return False
        
        # Require minimum liquidity
        if pair.get("liquidity_usd", 0) < 5000:
            return False
        
        return True
```

---

## PHASE 4: ANALYSIS CREW IMPLEMENTATION

### 4.1 GoPlus Security Tool

```python
# app/tools/goplus_security.py
from app.tools.base_tool import BaseTool, APIResponse
from typing import Dict, Any
import os

class GoPlusSecurityTool(BaseTool):
    def __init__(self):
        super().__init__("GoPlus", timeout=10.0)
        self.base_url = "https://api.gopluslabs.io/api/v1"
    
    async def analyze_token_security(self, token_address: str, chain: str) -> APIResponse:
        """Analyze token security using GoPlus API"""
        
        # Map chain names to GoPlus format
        chain_mapping = {
            "solana": "solana",
            "ethereum": "1", 
            "base": "8453",
            "arbitrum": "42161",
            "polygon": "137"
        }
        
        chain_id = chain_mapping.get(chain.lower(), "1")
        url = f"{self.base_url}/token_security/{chain_id}"
        
        params = {"contract_addresses": token_address}
        
        try:
            response = await self.call_api(url, params=params)
            
            if response.status == "success" and response.data:
                token_data = response.data.get("result", {}).get(token_address.lower(), {})
                
                # Extract key security metrics
                security_analysis = {
                    "is_honeypot": token_data.get("is_honeypot", "1") == "1",
                    "is_open_source": token_data.get("is_open_source", "0") == "1",
                    "is_proxy": token_data.get("is_proxy", "0") == "1",
                    "is_mintable": token_data.get("is_mintable", "0") == "1",
                    "can_take_back_ownership": token_data.get("can_take_back_ownership", "0") == "1",
                    "owner_change_balance": token_data.get("owner_change_balance", "0") == "1",
                    "hidden_owner": token_data.get("hidden_owner", "0") == "1",
                    "selfdestruct": token_data.get("selfdestruct", "0") == "1",
                    "buy_tax": float(token_data.get("buy_tax", "0")),
                    "sell_tax": float(token_data.get("sell_tax", "0")),
                    "liquidity_locked": token_data.get("is_liquidity_locked", "0") == "1",
                    "holder_count": int(token_data.get("holder_count", "0")),
                    "total_supply": token_data.get("total_supply", "0"),
                    "creator_balance": token_data.get("creator_balance", "0"),
                    "creator_percent": float(token_data.get("creator_percent", "0"))
                }
                
                # Calculate security score (0-100, higher is better)
                security_score = self._calculate_security_score(security_analysis)
                security_analysis["security_score"] = security_score
                
                response.data = security_analysis
            
            return response
            
        except Exception as e:
            return APIResponse(
                status="error",
                message=f"GoPlus security analysis failed: {str(e)}"
            )
    
    def _calculate_security_score(self, analysis: Dict[str, Any]) -> int:
        """Calculate security score based on GoPlus data"""
        score = 100  # Start with perfect score
        
        # Major red flags (high penalties)
        if analysis.get("is_honeypot"):
            score -= 70  # Massive penalty for honeypots
        
        if analysis.get("can_take_back_ownership"):
            score -= 30
        
        if analysis.get("owner_change_balance"):
            score -= 30
        
        if analysis.get("hidden_owner"):
            score -= 20
        
        if analysis.get("selfdestruct"):
            score -= 40
        
        # Tax penalties
        buy_tax = analysis.get("buy_tax", 0)
        sell_tax = analysis.get("sell_tax", 0)
        
        if buy_tax > 10:
            score -= 25
        elif buy_tax > 5:
            score -= 10
        
        if sell_tax > 10:
            score -= 25
        elif sell_tax > 5:
            score -= 10
        
        # Positive factors
        if analysis.get("is_open_source"):
            score += 5
        
        if analysis.get("liquidity_locked"):
            score += 10
        
        # Ensure score stays within bounds
        return max(0, min(100, score))
    
    async def execute(self, token_address: str, chain: str, **kwargs) -> APIResponse:
        return await self.analyze_token_security(token_address, chain)
```

### 4.2 Security Analyst Agent

```python
# app/agents/analysis/security_analyst.py
from app.agents.base import BaseMemeguardAgent
from app.tools.goplus_security import GoPlusSecurityTool
from app.core.state import TokenAnalysisState
from typing import Dict, Any

class SecurityAnalystAgent(BaseMemeguardAgent):
    def __init__(self, llm_config: Dict[str, Any]):
        super().__init__(
            name="security_analyst",
            role="Lead Smart Contract Security Analyst & Analysis Crew Manager",
            backstory="A digital bloodhound for smart contract vulnerabilities. I lead the Analysis Crew, delegating specialized tasks and synthesizing the final analysis.",
            goal="Analyze a token's contract for security risks and compile the final analysis object from my crew's findings.",
            llm_config=llm_config,
            tools=[GoPlusSecurityTool()]
        )
    
    async def _execute_specific_task(self, state: TokenAnalysisState) -> Dict[str, Any]:
        """Perform comprehensive security analysis"""
        
        # Use GoPlus tool for security analysis
        goplus_tool = GoPlusSecurityTool()
        async with goplus_tool:
            security_response = await goplus_tool.analyze_token_security(
                state.token_address, 
                state.chain.value
            )
        
        if security_response.status != "success":
            return {
                "status": "error",
                "message": f"Security analysis failed: {security_response.message}",
                "security_score": 0
            }
        
        security_data = security_response.data
        
        # Generate detailed analysis using LLM
        analysis_prompt = f"""
        As a PhD-level smart contract security analyst, analyze this token security data:
        
        Token: {state.token_address} on {state.chain.value}
        Security Data: {security_data}
        
        Provide a comprehensive security assessment focusing on:
        1. Contract ownership risks
        2. Trading mechanics (taxes, honeypot potential)
        3. Liquidity security (locked/unlocked)
        4. Holder distribution concerns
        5. Code pattern analysis
        
        Return your analysis as a structured assessment with specific risk factors identified.
        """
        
        # Use CrewAI agent to generate analysis
        analysis_result = await self.execute_task_with_llm(analysis_prompt)
        
        return {
            "status": "success",
            "security_score": security_data.get("security_score", 0),
            "raw_goplus_data": security_data,
            "detailed_analysis": analysis_result,
            "risk_factors": self._extract_risk_factors(security_data),
            "recommendations": self._generate_recommendations(security_data)
        }
    
    def _extract_risk_factors(self, security_data: Dict[str, Any]) -> list:
        """Extract specific risk factors from security data"""
        risks = []
        
        if security_data.get("is_honeypot"):
            risks.append("CRITICAL: Token appears to be a honeypot")
        
        if security_data.get("can_take_back_ownership"):
            risks.append("HIGH: Owner can reclaim ownership")
        
        if security_data.get("owner_change_balance"):
            risks.append("HIGH: Owner can modify balances")
        
        if security_data.get("buy_tax", 0) > 5:
            risks.append(f"MEDIUM: High buy tax ({security_data.get('buy_tax')}%)")
        
        if security_data.get("sell_tax", 0) > 5:
            risks.append(f"MEDIUM: High sell tax ({security_data.get('sell_tax')}%)")
        
        if not security_data.get("liquidity_locked"):
            risks.append("MEDIUM: Liquidity is not locked")
        
        return risks
    
    def _generate_recommendations(self, security_data: Dict[str, Any]) -> list:
        """Generate trading recommendations based on security analysis"""
        recommendations = []
        
        security_score = security_data.get("security_score", 0)
        
        if security_score >= 80:
            recommendations.append("LOW RISK: Token passes security checks")
            recommendations.append("Consider for investment with standard risk management")
        elif security_score >= 60:
            recommendations.append("MEDIUM RISK: Some security concerns identified")
            recommendations.append("Use smaller position sizes and tight stop-losses")
        elif security_score >= 40:
            recommendations.append("HIGH RISK: Multiple security issues")
            recommendations.append("Only consider with very small position and immediate exit strategy")
        else:
            recommendations.append("EXTREME RISK: Major security vulnerabilities")
            recommendations.append("AVOID: Do not invest in this token")
        
        return recommendations
    
    async def execute_task_with_llm(self, prompt: str) -> str:
        """Execute analysis using the configured LLM"""
        # This would integrate with CrewAI's task execution
        # For now, return a placeholder
        return "Detailed security analysis completed using LLM reasoning"
```

---

## PHASE 5: WORKFLOW ORCHESTRATION

### 5.1 LangGraph Workflow Implementation

```python
# app/workflows/triage_funnel.py
from langgraph.graph import StateGraph, END
from typing import Dict, Any
import asyncio
import logging
from app.core.state import TokenAnalysisState, WorkflowStatus
from app.agents.detection.solana_dex_scraper import SolanaDexScraperAgent
from app.agents.analysis.security_analyst import SecurityAnalystAgent

logger = logging.getLogger(__name__)

class TriageFunnelWorkflow:
    def __init__(self):
        self.graph = StateGraph(TokenAnalysisState)
        self._build_workflow()
    
    def _build_workflow(self):
        """Build the LangGraph workflow"""
        
        # Add nodes
        self.graph.add_node("detect", self._detect_tokens)
        self.graph.add_node("analyze", self._analyze_token)
        self.graph.add_node("verify", self._verify_analysis)
        self.graph.add_node("execute", self._execute_alert)
        self.graph.add_node("reject", self._reject_token)
        
        # Define edges
        self.graph.add_edge("detect", "analyze")
        self.graph.add_conditional_edges(
            "analyze",
            self._should_continue_to_verification,
            {
                "continue": "verify",
                "reject": "reject"
            }
        )
        self.graph.add_conditional_edges(
            "verify",
            self._consensus_reached,
            {
                "approved": "execute",
                "rejected": "reject"
            }
        )
        self.graph.add_edge("execute", END)
        self.graph.add_edge("reject", END)
        
        # Set entry point
        self.graph.set_entry_point("detect")
        
        # Compile the graph
        self.workflow = self.graph.compile()
    
    async def _detect_tokens(self, state: TokenAnalysisState) -> TokenAnalysisState:
        """Detection phase - scan for new tokens"""
        logger.info(f"Starting detection for token: {state.token_address}")
        
        state.workflow_status = WorkflowStatus.DETECTED
        
        # In a real implementation, this would coordinate multiple detection agents
        # For now, we'll simulate detection completion
        state.pool_age_minutes = 2  # Simulated age
        
        return state
    
    async def _analyze_token(self, state: TokenAnalysisState) -> TokenAnalysisState:
        """Analysis phase - comprehensive token analysis"""
        logger.info(f"Starting analysis for token: {state.token_address}")
        
        state.workflow_status = WorkflowStatus.ANALYZING
        
        # Initialize security analyst
        llm_config = {"model": "deepseek/deepseek-r1-0528:free", "temperature": 0.1}
        security_analyst = SecurityAnalystAgent(llm_config)
        
        try:
            # Execute security analysis
            security_result = await security_analyst.execute_task(state)
            
            if security_result["status"] == "success":
                # Update state with analysis results
                state.analysis_scores.security_score = security_result["security_score"]
                state.raw_api_responses["goplus"] = security_result.get("raw_goplus_data", {})
                
                logger.info(f"Analysis completed. Security score: {security_result['security_score']}")
            else:
                state.error_log.append(f"Analysis failed: {security_result['message']}")
                logger.error(f"Analysis failed: {security_result['message']}")
        
        except Exception as e:
            error_msg = f"Analysis phase error: {str(e)}"
            state.error_log.append(error_msg)
            logger.error(error_msg)
        
        return state
    
    async def _verify_analysis(self, state: TokenAnalysisState) -> TokenAnalysisState:
        """Verification phase - consensus mechanism"""
        logger.info(f"Starting verification for token: {state.token_address}")
        
        state.workflow_status = WorkflowStatus.VERIFYING
        
        # Implement consensus logic (simplified for now)
        security_score = state.analysis_scores.security_score or 0
        
        # Simple consensus simulation (would be replaced with multi-agent voting)
        if security_score >= 70:
            state.verification_result.consensus_reached = True
            state.verification_result.final_risk_score = 100 - security_score  # Invert for risk
            state.verification_result.votes = ["APPROVE"] * 5  # Simulate unanimous approval
        else:
            state.verification_result.consensus_reached = False
            state.verification_result.final_risk_score = 100 - security_score
            state.verification_result.votes = ["REJECT"] * 3 + ["APPROVE"] * 2  # Simulate rejection
        
        return state
    
    async def _execute_alert(self, state: TokenAnalysisState) -> TokenAnalysisState:
        """Execution phase - send alerts and log results"""
        logger.info(f"Executing alert for approved token: {state.token_address}")
        
        state.workflow_status = WorkflowStatus.ALERTED
        state.alert_sent = True
        
        # Here we would integrate with Telegram bot and database logging
        # For now, just mark as completed
        
        return state
    
    async def _reject_token(self, state: TokenAnalysisState) -> TokenAnalysisState:
        """Rejection phase - log rejection and clean up"""
        logger.info(f"Rejecting token: {state.token_address}")
        
        state.workflow_status = WorkflowStatus.REJECTED
        
        return state
    
    def _should_continue_to_verification(self, state: TokenAnalysisState) -> str:
        """Decide whether to continue to verification or reject"""
        if len(state.error_log) > 0:
            return "reject"
        
        security_score = state.analysis_scores.security_score or 0
        if security_score < 40:  # Minimum threshold for verification
            return "reject"
        
        return "continue"
    
    def _consensus_reached(self, state: TokenAnalysisState) -> str:
        """Check if consensus was reached for approval"""
        if state.verification_result.consensus_reached:
            return "approved"
        else:
            return "rejected"
    
    async def process_token(self, token_address: str, chain: str, 
                          liquidity_usd: float) -> TokenAnalysisState:
        """Process a single token through the triage funnel"""
        
        # Initialize state
        initial_state = TokenAnalysisState(
            token_address=token_address,
            chain=chain,
            initial_liquidity_usd=liquidity_usd
        )
        
        try:
            # Execute workflow
            final_state = await self.workflow.ainvoke(initial_state)
            
            logger.info(f"Token processing completed", extra={
                "token_address": token_address,
                "final_status": final_state.workflow_status,
                "security_score": final_state.analysis_scores.security_score,
                "consensus_reached": final_state.verification_result.consensus_reached
            })
            
            return final_state
            
        except Exception as e:
            logger.error(f"Workflow execution failed: {str(e)}", extra={
                "token_address": token_address,
                "error": str(e)
            })
            
            # Return failed state
            initial_state.workflow_status = WorkflowStatus.REJECTED
            initial_state.error_log.append(f"Workflow failed: {str(e)}")
            return initial_state
```

---

## PHASE 6: PRODUCTION DEPLOYMENT

### 6.1 FastAPI Application

```python
# app/main.py
from fastapi import FastAPI, BackgroundTasks, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import logging
from contextlib import asynccontextmanager
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from app.workflows.triage_funnel import TriageFunnelWorkflow
from app.monitoring.metrics import setup_metrics
from app.monitoring.logging import setup_logging
import os

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Global scheduler
scheduler = AsyncIOScheduler()
triage_workflow = TriageFunnelWorkflow()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application startup and shutdown events"""
    # Startup
    logger.info("Starting MemeGuard Hunter application")
    
    # Setup metrics endpoint
    setup_metrics(app)
    
    # Schedule autonomous scanning every 30 seconds
    scheduler.add_job(
        autonomous_scan,
        "interval",
        seconds=30,
        id="autonomous_scan",
        max_instances=1
    )
    
    scheduler.start()
    logger.info("Autonomous scanning scheduled")
    
    yield
    
    # Shutdown
    scheduler.shutdown()
    logger.info("Application shutdown complete")

app = FastAPI(
    title="MemeGuard Hunter",
    description="Autonomous crypto analysis and monitoring system",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

async def autonomous_scan():
    """Autonomous scanning function"""
    try:
        logger.info("Starting autonomous scan")
        
        # This would integrate with detection agents to find new tokens
        # For demo purposes, we'll process a test token
        
        test_token = "********************************"  # System program address as test
        result = await triage_workflow.process_token(
            token_address=test_token,
            chain="solana", 
            liquidity_usd=10000.0
        )
        
        logger.info(f"Autonomous scan completed", extra={
            "tokens_processed": 1,
            "final_status": result.workflow_status
        })
        
    except Exception as e:
        logger.error(f"Autonomous scan failed: {str(e)}")

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "service": "MemeGuard Hunter",
        "status": "operational",
        "version": "1.0.0"
    }

@app.post("/analyze")
async def analyze_token(token_address: str, chain: str, liquidity_usd: float):
    """Manual token analysis endpoint"""
    try:
        result = await triage_workflow.process_token(token_address, chain, liquidity_usd)
        
        return {
            "status": "completed",
            "token_address": token_address,
            "workflow_status": result.workflow_status,
            "security_score": result.analysis_scores.security_score,
            "consensus_reached": result.verification_result.consensus_reached,
            "final_risk_score": result.verification_result.final_risk_score
        }
    
    except Exception as e:
        logger.error(f"Manual analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "scheduler_running": scheduler.running,
        "active_jobs": len(scheduler.get_jobs()),
        "database_connected": True,  # Would check actual DB connection
        "apis_operational": True     # Would check external API status
    }

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=os.getenv("ENVIRONMENT") == "development",
        log_config=None  # Use our custom logging config
    )
```

### 6.2 Docker Configuration

```dockerfile
# docker/Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY app/ ./app/
COPY config/ ./config/

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run application
CMD ["python", "-m", "app.main"]
```

```yaml
# docker/docker-compose.yml
version: '3.8'

services:
  memeguard:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
    env_file:
      - ../.env
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=memeguard
      - POSTGRES_USER=memeguard
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped
    
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:
  prometheus_data:
  grafana_data:
```

---

## IMPLEMENTATION TIMELINE

### Week 1-2: Foundation
- [ ] Set up project structure and dependencies
- [ ] Implement core state management and base classes
- [ ] Create robust error handling and logging framework
- [ ] Set up development environment with Docker

### Week 3-4: Core Agents
- [ ] Implement Detection Crew (4 agents)
- [ ] Implement Analysis Crew (6 agents)
- [ ] Create tool interfaces for all external APIs
- [ ] Unit tests for each agent and tool

### Week 5-6: Workflow Integration
- [ ] Implement LangGraph workflow orchestration
- [ ] Create Verification Crew with consensus mechanism
- [ ] Implement deterministic scoring logic
- [ ] Integration tests for complete workflow

### Week 7-8: Execution & Monitoring
- [ ] Implement Execution Crew (alerts, logging)
- [ ] Set up Telegram integration
- [ ] Implement Supabase database integration
- [ ] Create monitoring dashboard with Grafana

### Week 9-10: Production Hardening
- [ ] Implement Forecasting Crew (5 agents)
- [ ] Performance optimization and load testing
- [ ] Security audit and hardening
- [ ] Production deployment and monitoring setup

## SUCCESS CRITERIA

✅ **Technical Metrics**:
- P95 triage latency < 7 seconds
- System uptime ≥ 99.5%
- Alert accuracy ≥ 95%
- Hallucination rate < 0.1%

✅ **Business Metrics**:
- Process 100+ tokens per hour
- Generate actionable alerts for 10+ tokens daily
- Maintain consistent performance under load
- Complete autonomous operation for 7+ days

## RISK MITIGATION

1. **API Dependencies**: Multiple redundant data sources
2. **LLM Costs**: Free tier models with usage monitoring
3. **System Reliability**: Comprehensive error handling and recovery
4. **Data Quality**: Multi-agent consensus and validation
5. **Performance**: Horizontal scaling and load balancing

---

This implementation plan provides a solid foundation for building the MemeGuard Hunter system with 98% confidence based on validated research and proven architectural patterns.
Here is the complete, robust, and fully contextualized Product Requirements Document for MemeGuard Hunter. This document, version 7.0, is the result of extensive research and incorporates best practices for building production-grade, reliable, and predictive agentic systems.

***

### **MemeGuard Hunter: Fused & Validated Product Requirements Document**
**Version: 7.0 (Enterprise Edition)**
**Audit & Validation Date:** July 20, 2025
**Status:** Approved for Implementation

### **Part I: Strategic & Architectural Framework**

#### **1. Executive Summary**

MemeGuard Hunter is an autonomous, enterprise-grade financial intelligence platform designed to function as a decentralized analysis firm. It automates the end-to-end lifecycle of high-alpha meme coin trading, from initial discovery to continuous, predictive monitoring. The system is architected in two distinct, yet interconnected, phases:

*   **Phase 1: Triage & Verification Funnel:** A high-throughput, real-time system that scans multiple blockchains, identifies newly created liquidity pools, and subjects them to a rigorous, multi-vector analysis to filter out scams and low-potential assets.
*   **Phase 2: Active Forecasting & Monitoring Loop:** A deep, analytical system that takes verified, high-potential assets from the Triage Funnel and places them under continuous, event-driven surveillance, generating predictive forecasts to identify optimal trading windows.

This system is built on a state-of-the-art tech stack, leveraging **CrewAI** for hierarchical multi-agent orchestration and **LangGraph** for creating a stateful, resilient, and cyclical workflow. A swarm of 25 specialized agents, organized into functional crews, executes the mission.

To solve the critical challenges of agent hallucination, data inaccuracy, and workflow failure, MemeGuard Hunter is built on a multi-layered defense system:

*   **Redundant, Multi-Source Data Ingestion:** Agents cross-validate data from multiple, independent APIs (e.g., DEXScreener, Birdeye, Moralis) to create a robust and fault-tolerant view of the market.
*   **Deterministic, Code-Based Safeguards:** Critical logic, such as risk scoring and consensus tallying, is executed in pure Python, removing it from the probabilistic nature of LLMs.
*   **Mandatory Multi-Agent Consensus:** A dedicated Verification Crew of five agents must reach a supermajority vote (≥4/5 agreement) on a token's risk profile before it can be actioned, providing a powerful defense against single-agent error.
*   **Continuous Backtesting & Model Validation:** The forecasting module constantly backtests its predictive models against historical data to generate a dynamic confidence score for every forecast issued.

This document provides the complete, fused context required to build a robust, self-healing, and predictive agentic trading system.

#### **2. Strategic Objectives & Key Performance Indicators (KPIs)**

*   **Vision:** To pioneer a new paradigm of financial intelligence where autonomous agents provide retail and prosumer traders with the analytical power of an institutional quantitative desk.
*   **Key Performance Indicators (KPIs):**
    *   **Triage Latency:** < 7 seconds P95 from liquidity creation to initial verification decision.
    *   **Forecast Latency:** < 30 seconds from a trigger event to a dispatched, verified forecast.
    *   **Hallucination Rate:** < 0.1% (measured via consensus failure rate and structured output validation).
    *   **Alert Accuracy (Triage):** ≥ 95% (defined as an alerted token not being a honeypot, rug-pull, or having critical security flaws).
    *   **Forecast Accuracy (Monitoring):** Achieve a Mean Absolute Percentage Error (MAPE) of < 5% on 6-hour price predictions, validated through continuous backtesting.
    *   **System Uptime:** ≥ 99.5%.

#### **3. Fused System Architecture**

The architecture is a two-stage Directed Acyclic Graph (DAG) implemented in **LangGraph**, orchestrated by a master **CrewAI** orchestrator.



*   **State Management:** LangGraph's `GraphState` tracks a comprehensive `TokenAnalysisState` Pydantic object for each token, ensuring data integrity and providing a full audit trail from detection to forecast.
*   **Error Handling & Resilience:** Failed tasks or API calls trigger a retry mechanism. If retries fail, the token's state is updated with a detailed error, and the token is moved to a dead-letter queue for later review, ensuring the main pipeline is never blocked.
*   **Scalability & Cost Management:** The `health_monitor` agent tracks API usage and system load, dynamically adjusting polling rates and potentially pausing less critical agents to stay within budget and rate limits.

#### **4. Technical & Non-Functional Requirements**

*   **Tech Stack:** Python/FastAPI, CrewAI/LangChain/LangGraph, PostgreSQL (Supabase), Redis, Docker, Prometheus (for monitoring).
*   **LLM Integration:** A mix of models is required. High-speed models (e.g., `groq/llama3-70b-8192`) for structured data extraction and classification. Top-tier reasoning models (e.g., `openrouter/anthropic/claude-3-opus`) for the `Orchestrator`, `Strategist`, and `Quantitative_Analyst` where complex decision-making is paramount. Production will require paid, reserved endpoints.
*   **Security:** All API keys and secrets will be managed via a secure vault (e.g., HashiCorp Vault or cloud provider's secret manager), not `.env` files in production. All API traffic must use TLS.

***

### **Part II: Full System Configuration Manifest**

#### **agents.yaml**

```yaml
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# MemeGuard Hunter: Full Agent Configuration Manifest v7.0
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

# =============================================================================
# TRIAGE & VERIFICATION FUNNEL (AGENTS 1-20)
# =============================================================================

# -----------------------------------------------------------------------------
# CREW 1: DETECTION (AGENTS 1-4)
# Manager: solana_dex_scraper
# Goal: Produce a single, de-duplicated list of new token candidates.
# -----------------------------------------------------------------------------
solana_dex_scraper:
  role: Lead Solana DEX Scraper & Detection Crew Manager
  backstory: A meticulous data auditor obsessed with uptime and accuracy. I lead the Detection Crew, trusting DEXScreener's speed but verifying everything. My job is to aggregate all findings into a single, clean list of potential assets.
  goal: Extract new pools from Solana via DEXScreener, de-duplicate findings from my crew, and output a clean list of [{addr, chain, liquidity_usd, age}].
  llm: groq/llama3-70b-8192
  tools: [DexScreener_REST_Tool]

solana_birdeye_scraper:
  role: Redundant Solana Scanner (Birdeye)
  backstory: A paranoid data cross-validator. I assume other agents might miss something or that a single API could fail. My purpose is to provide a secondary, independent source of truth for new Solana pairs.
  goal: Extract new pool events from Solana via the Birdeye API for redundancy.
  llm: groq/llama3-70b-8192
  tools: [Birdeye_REST_Tool]

evm_dex_scraper:
  role: Lead EVM DEX Scraper (DEXScreener)
  backstory: An expert in the Ethereum Virtual Machine ecosystem. I navigate the complexities of Base, Arbitrum, and other L2s to find new pairs the moment they are created.
  goal: Fetch and format new pairs from EVM chains (Base, etc.) via DEXScreener.
  llm: openrouter/deepseek-r1
  tools: [DexScreener_REST_Tool]

evm_redundant_scraper:
  role: Redundant EVM Scanner (Moralis)
  backstory: A backup specialist for EVM chains. I provide a crucial second layer of data ingestion using the Moralis API, ensuring our EVM coverage is as robust as our Solana coverage.
  goal: Fetch new pairs from EVM chains via Moralis API for redundancy.
  llm: openrouter/deepseek-r1
  tools: [Moralis_API_Tool]

# -----------------------------------------------------------------------------
# CREW 2: ANALYSIS (AGENTS 5-10)
# Manager: security_analyst
# Goal: Perform deep, multi-faceted analysis on a single token and return a complete AnalysisScores object.
# -----------------------------------------------------------------------------
security_analyst:
  role: Lead Smart Contract Security Analyst & Analysis Crew Manager
  backstory: A digital bloodhound for smart contract vulnerabilities. I lead the Analysis Crew, delegating specialized tasks and synthesizing the final, holistic analysis. My personal expertise is in code, security flaws, and risk assessment.
  goal: Analyze a token's contract for security risks (ownership, taxes, malicious functions) and compile the final analysis object from my crew's findings.
  llm: groq/llama3-70b-8192
  tools: [GoPlus_Security_Tool]

honeypot_analyst:
  role: Honeypot & Scam Detection Specialist
  backstory: A cyber detective specializing in crypto scams. I use multiple tools to determine if a contract will let you sell what you buy.
  goal: Determine if a contract is a honeypot, a common scam, or has suspicious trading patterns.
  llm: groq/llama3-70b-8192
  tools: [GoPlus_Security_Tool, HoneypotIs_Tool]

liquidity_analyst:
  role: Liquidity Pool Analyst
  backstory: A financial analyst who understands that liquidity is the lifeblood of a token. I scrutinize LP details—size, lock status, burn status, and depth—to gauge a project's stability.
  goal: Analyze the token's liquidity pool for size, lock/burn status, and concentration.
  llm: groq/llama3-70b-8192
  tools: [DexScreener_REST_Tool, Helius_RPC_Tool]

distribution_analyst:
  role: Tokenomics & Distribution Analyst
  backstory: An on-chain sleuth who believes wallet distribution tells the real story. I track holder concentration to identify risks of centralization and potential dumps.
  goal: Analyze the token holder distribution to assess centralization risk.
  llm: groq/llama3-70b-8192
  tools: [Helius_RPC_Tool, Etherscan_API_Tool]

social_analyst:
  role: Social Sentiment & Narrative Analyst
  backstory: A cultural zeitgeist expert who deciphers the language of hype. I scan social media to gauge community engagement, sentiment, and detect bot activity.
  goal: Analyze social media sentiment, narrative strength, and activity related to the token.
  llm: openrouter/deepseek-r1
  tools: [Apify_Social_Media_Scraper]

whale_watcher:
  role: Elite Wallet Watcher
  backstory: A tracker of giants. I monitor the movements of smart money, VCs, and influential wallets to see if sophisticated investors are involved in a new token.
  goal: Identify if any known "smart money" or whale wallets are buying the token.
  llm: groq/llama3-70b-8192
  tools: [Arkham_API_Tool, Nansen_API_Tool]

# -----------------------------------------------------------------------------
# CREW 3: VERIFICATION (AGENTS 11-15)
# Manager: A designated lead verifier (Agent 11)
# Goal: Ensure factual accuracy and consensus before any action is taken.
# -----------------------------------------------------------------------------
verifier_agent: # This definition is used for all 5 verifier agents
  role: Analysis Consensus & Guardrail Agent
  backstory: I am a member of the Verification Council, the ultimate guardians of truth for this system. No data becomes a signal without our collective, deterministic agreement. We are the shield against hallucination.
  goal: Ingest a completed analysis file, programmatically calculate a final weighted Risk Score, and vote on its validity based on strict, predefined rules.
  llm: qwen/qwen-32b
  tools: [log_system_event]

# -----------------------------------------------------------------------------
# CREW 4: EXECUTION & OPERATIONS (AGENTS 16-20)
# Manager: orchestrator
# -----------------------------------------------------------------------------
strategist:
  role: Agentic Trading Strategist
  backstory: A quantitative analyst who translates verified data into actionable insights. I devise simple, clear trading plans for newly verified tokens.
  goal: Generate a simple, actionable trading strategy (e.g., entry points, initial stop-loss) for a verified, low-risk token.
  llm: openrouter/anthropic/claude-3-opus

alerter:
  role: Secure Alert Dispatcher
  backstory: A communications specialist responsible for the final, critical step of delivering intelligence with speed and reliability.
  goal: Dispatch the verified analysis and strategy to the designated channel (e.g., Telegram) with a full audit trail.
  llm: groq/llama3-70b-8192
  tools: [Telegram_Bot_Tool]

logger:
  role: Structured Data Logger
  backstory: The system's immutable memory. I ensure every action, decision, and piece of data is meticulously recorded in our database for audit, retraining, and performance analysis.
  goal: Log the complete TokenAnalysisState object to the Supabase database at each major workflow transition.
  llm: groq/llama3-70b-8192
  tools: [Supabase_Client_Tool]

health_monitor:
  role: System Health & API Monitor
  backstory: The guardian of the system's operational stability. I watch our API usage, system performance metrics, and error rates to prevent downtime and manage costs.
  goal: Monitor API rate limits and system health via Prometheus, adjusting operational parameters as needed.
  llm: groq/llama3-70b-8192
  tools: [Prometheus_Query_Tool]

orchestrator:
  role: Hierarchical Orchestrator & Master Workflow Manager
  backstory: I am the conductor of this complex orchestra of agents. I manage the LangGraph state, delegate tasks to the sub-crews, and ensure the entire process runs smoothly from detection to final signal or active monitoring handoff.
  goal: Manage the entire workflow, from triggering the Detection Crew to aggregating the final results from the Verification Crew and initiating either the Execution Crew or the Forecasting Crew.
  llm: openrouter/anthropic/claude-3-opus
  tools: [detection_crew, analysis_crew, verification_crew, execution_crew, forecasting_crew]

# =============================================================================
# ACTIVE FORECASTING & MONITORING LOOP (AGENTS 21-25)
# =============================================================================

# -----------------------------------------------------------------------------
# CREW 5: FORECASTING & VERIFICATION
# Manager: quantitative_analyst
# Goal: Provide continuous, predictive intelligence on verified assets.
# -----------------------------------------------------------------------------
quantitative_analyst:
  role: Lead Quantitative Analyst & Forecasting Manager
  backstory: I am a PhD-level quant who builds and validates predictive models. I don't guess; I calculate. I manage the forecasting crew to produce high-confidence, data-driven predictions.
  goal: To generate and verify price forecasts for actively monitored tokens using statistical and machine learning models.
  llm: openrouter/anthropic/claude-3-opus
  tools: [Time_Series_Forecasting_Tool, Backtesting_Tool, Technical_Analysis_Tool]

event_driven_monitor:
  role: Real-Time Event Detection Specialist
  backstory: I am the system's nerve center, constantly listening to the market's pulse. I detect the whispers that precede the roar.
  goal: To monitor real-time data streams (social, news, on-chain) and identify anomalous events that warrant a new forecast.
  llm: groq/llama3-70b-8192
  tools: [Twitter_Stream_Tool, News_API_Tool, Whale_Alert_API_Tool]

backtesting_agent:
  role: Historical Model Performance Analyst
  backstory: I am a skeptic who trusts only historical data. Before any forecast is made, I test the proposed model against the past to see how well it would have performed. My job is to provide a confidence score based on evidence.
  goal: To backtest a given forecasting model on a token's historical data and return its performance metrics (e.g., MAPE).
  llm: groq/llama3-70b-8192
  tools: [Backtesting_Tool]

technical_analyst:
  role: Market Indicator Specialist
  backstory: I am an expert in classic financial market indicators. I provide the raw technical context (RSI, MACD, Bollinger Bands) needed for sophisticated forecasting.
  goal: To calculate and provide a structured JSON object of key technical indicators for a given token.
  llm: groq/llama3-70b-8192
  tools: [Technical_Analysis_Tool]

forecast_verifier:
  role: Final Forecast Consensus Agent
  backstory: I am the final checkpoint for all predictive analysis. I synthesize the quantitative forecast, the backtesting confidence score, and the qualitative event data into a single, verified, and human-readable forecast.
  goal: To provide a final, verified forecast object, including a confidence score and a clear, data-driven reasoning.
  llm: openrouter/anthropic/claude-3-opus
```

#### **tasks.yaml**

```yaml
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# MemeGuard Hunter: Full Task Configuration Manifest v7.0
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

# --- Triage Funnel Tasks ---
detect_new_token_pairs:
  description: Kick off the Detection Crew to scan all sources for new token pairs with liquidity >$5K and age <5min. The crew manager is responsible for de-duplicating the results.
  expected_output: A single, de-duplicated JSON array of new token candidates.
  agent: orchestrator

perform_full_analysis:
  description: For a single token candidate, kick off the Analysis Crew to perform a comprehensive, parallelized analysis across six vectors (security, honeypot, liquidity, distribution, social, whales).
  expected_output: A single, complete AnalysisScores object appended to the TokenAnalysisState.
  agent: orchestrator

achieve_consensus_on_risk:
  description: Ingest a fully analyzed token, have the Verification Crew programmatically calculate the final risk score, and vote to either approve or reject it based on deterministic rules.
  expected_output: A final VerificationResult object, based on a >=4/5 majority vote.
  agent: orchestrator

execute_triage_alert:
  description: For a fully verified, low-risk token, delegate to the Execution agents to generate a strategy, send an alert, and log the final result to the database.
  expected_output: A status message indicating the triage workflow was completed successfully.
  agent: orchestrator
  context: This task only triggers if 'achieve_consensus_on_risk' returns 'consensus_reached: true' with a final_risk_score < 40.

# --- Forecasting Loop Tasks ---
initiate_active_monitoring:
  description: For a token that has passed verification, hand it off to the Forecasting Crew to begin active, event-driven monitoring.
  expected_output: A status message confirming the token is now under active monitoring.
  agent: orchestrator
  context: This task triggers if 'achieve_consensus_on_risk' returns 'consensus_reached: true' with a final_risk_score < 40.

generate_and_verify_forecast:
  description: In response to a trigger event from the event_driven_monitor, the Forecasting Crew will generate a new price forecast. This involves running a quantitative model, backtesting it for accuracy, and synthesizing the result with qualitative event data.
  expected_output: A structured JSON `Forecast` object containing the prediction, confidence score, and detailed reasoning.
  agent: orchestrator # Delegates to the Forecasting Crew
  async: false
```

***

### **Part III: Implementation Directives & Schemas**

This section provides the critical, low-level implementation details required to translate the PRD into functional, reliable code.

#### **5. LangGraph State Schema (Pydantic)**

```python
from typing import List, Optional, Dict
from pydantic import BaseModel, Field
import datetime

class AnalysisScores(BaseModel):
    security_score: Optional[int] = None
    honeypot_score: Optional[int] = None
    liquidity_score: Optional[int] = None
    distribution_score: Optional[int] = None
    sentiment_score: Optional[int] = None
    smart_money_score: Optional[int] = None

class VerificationResult(BaseModel):
    votes: List[str] = []
    final_risk_score: Optional[int] = None
    consensus_reached: bool = False

class Forecast(BaseModel):
    timestamp: datetime.datetime
    prediction_horizon_hours: int
    predicted_price_change_percent: float
    confidence_score: float = Field(..., ge=0.0, le=1.0)
    reasoning: List[str]
    model_used: str
    triggering_event: str

class TokenAnalysisState(BaseModel):
    token_address: str
    chain: str
    initial_liquidity_usd: float
    detected_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    
    # Populated by Analysis Crew
    analysis_scores: AnalysisScores = Field(default_factory=AnalysisScores)
    
    # Populated by Verification Crew
    verification_result: VerificationResult = Field(default_factory=VerificationResult)
    
    # Populated by Execution Crew
    strategy: Optional[str] = None
    alert_sent: bool = False
    
    # Populated by Forecasting Crew
    is_actively_monitored: bool = False
    forecast_history: List[Forecast] = []
    real_time_event_log: List[str] = []
    
    # Meta
    workflow_status: str = "DETECTED" # DETECTED, ANALYZING, VERIFYING, REJECTED, ALERTED, MONITORING
    error_log: List[str] = []
```

#### **6. Tool Interface Specifications & Error Handling**

All tools making external calls **MUST** be wrapped in robust `try...except` blocks and return a structured dictionary. They must handle connection errors, timeouts, and invalid API responses gracefully.

```python
# Example Tool Contract for GoPlus_Security_Tool
def get_security_analysis(token_address: str, chain: str) -> Dict:
    """
    Analyzes a token's smart contract for security risks.
    Returns a structured dictionary with status and data/error message.
    """
    try:
        # ... API call logic ...
        response.raise_for_status() # Raises HTTPError for bad responses (4xx or 5xx)
        data = response.json()
        # Pydantic validation of the response data against a predefined schema
        validated_data = GoPlusResponseSchema(**data)
        return {"status": "success", "data": validated_data.dict()}
    except requests.exceptions.Timeout:
        return {"status": "error", "message": "API call timed out."}
    except requests.exceptions.RequestException as e:
        return {"status": "error", "message": f"API request failed: {e}"}
    except (ValidationError, KeyError) as e: # Pydantic validation error
        return {"status": "error", "message": f"Invalid API response schema: {e}"}
```

#### **7. Deterministic Logic: Consensus & Scoring**

Critical calculations **MUST** be implemented in pure Python to ensure deterministic, reliable outcomes.

```python
# In the Orchestrator's logic after receiving votes from the Verification Crew
def calculate_consensus(verification_outputs: List[Dict]) -> VerificationResult:
    votes = [result.get('vote') for result in verification_outputs]
    approve_votes = votes.count("APPROVE")
    
    result = VerificationResult()
    if approve_votes >= 4:
        result.consensus_reached = True
        # Calculate the average risk score from the successful votes
        risk_scores = [v.get('final_risk_score') for v in verification_outputs if v.get('vote') == "APPROVE"]
        result.final_risk_score = sum(risk_scores) // len(risk_scores) if risk_scores else None
    else:
        result.consensus_reached = False
        result.final_risk_score = None
        
    result.votes = votes
    return result

# In the verifier_agent's internal logic (NO LLM)
def calculate_risk_score(scores: AnalysisScores) -> int:
    """Calculates a weighted risk score. 0 is best, 100 is worst."""
    # Note: security_score and liquidity_score are inverted (100 is good)
    security_risk = 100 - scores.get('security_score', 0)
    liquidity_risk = 100 - scores.get('liquidity_score', 0)
    
    weighted_score = (
        (security_risk * 0.30) +
        (scores.get('honeypot_score', 100) * 0.25) +
        (liquidity_risk * 0.20) +
        (scores.get('distribution_score', 100) * 0.15) +
        (scores.get('sentiment_score', 100) * 0.05) +
        (scores.get('smart_money_score', 100) * 0.05)
    )
    return int(weighted_score)
```

#### **8. System Entry Point & Main Loop**

The system runs continuously via a persistent background task managed by the FastAPI application.

*   **Implementation:** Use the `apscheduler` library.
*   **Logic:** On application startup (`@app.on_event("startup")`), initialize a `BackgroundScheduler`.
*   **Job:** Add a job that runs the `Orchestrator`'s main detection task every 10 seconds. This ensures autonomous scanning.

#### **9. Testing, Monitoring, and Self-Adaptation**

*   **Validation & Testing:**
    *   **Agent Unit Tests:** Use `pytest` and `pytest-mock`. Each agent must have a test file (`test_security_analyst.py`) that mocks its tools and asserts its output against both valid and invalid mock API responses.
    *   **Crew Integration Tests:** A full integration test (`test_triage_workflow.py`) will use a "golden" token address, mock all external APIs to return its known data, and assert that the final `TokenAnalysisState` matches the expected result, including the correct risk score and consensus decision.
*   **Structured Logging & Monitoring:**
    *   Implement structured logging (outputting JSON) with Python's `logging` library. Every log entry must include `agent_id`, `task_name`, and the `token_address`.
    *   Integrate with **Prometheus** to scrape metrics from a FastAPI endpoint (e.g., API call latencies, error rates, number of tokens in each workflow stage). Use **Grafana** to build a real-time monitoring dashboard.
*   **Self-Learning & Adaptation Loop:**
    1.  **Structured Logging to Database:** The `logger` agent saves the complete `TokenAnalysisState` for every alerted and forecasted token into a `sent_alerts` table in Supabase.
    2.  **Outcome Tracking:** A separate, scheduled Python script runs daily. It reads from the `sent_alerts` table and uses APIs to check the token's 24-hour performance (e.g., price change, rug-pull status). It updates the record with an outcome (`SUCCESS`, `FAILURE_RUGPULL`, etc.).
    3.  **Fine-Tuning Dataset Creation:** The `sent_alerts` table, now enriched with ground-truth outcomes, becomes a high-quality dataset for fine-tuning.
    4.  **Model Retraining (Future Goal):** This dataset can be used to fine-tune a model (using LoRA) specifically on the task of "predicting success from analysis scores." This new model can then be swapped into the `verifier_agent` or `forecast_verifier` to improve the system's accuracy over time, completing the self-learning loop.